{"logs": [{"outputFile": "com.autolink.sbjk.app-mergeDebugResources-35:/values-night-v8/values-night-v8.xml", "map": [{"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\picker_styles.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "1224", "endLines": "26", "endColumns": "12", "endOffsets": "1522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0e0dd431addcd28d83cc92e48e3c0db\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "27,28,29,30,31,32,33,74", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1527,1597,1681,1765,1861,1963,2065,5778", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1592,1676,1760,1856,1958,2060,2154,5862"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5047", "endLines": "73", "endColumns": "12", "endOffsets": "5773"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,40,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2254,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,67,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2317,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,106,168,232,295,350,414,502,568,632,690,748,807,875,935,990,1041,1110,1166", "endColumns": "50,61,63,62,54,63,87,65,63,57,57,58,67,59,54,50,68,55,57", "endOffsets": "101,163,227,290,345,409,497,563,627,685,743,802,870,930,985,1036,1105,1161,1219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31bbd90c8901d0911f3ed7bb95959bd7\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2159,2234,2345,2434,2535,2642,2749,2848,2955,3058,3185,3273,3397,3499,3601,3717,3819,3933,4061,4177,4299,4435,4555,4689,4809,4921,5867,5984,6108,6238,6360,6498,6632,6748", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2229,2340,2429,2530,2637,2744,2843,2950,3053,3180,3268,3392,3494,3596,3712,3814,3928,4056,4172,4294,4430,4550,4684,4804,4916,5042,5979,6103,6233,6355,6493,6627,6743,6863"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.autolink.sbjk.app-mergeDebugResources-35:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\picker_styles.xml", "from": {"startLines": "4", "startColumns": "4", "startOffsets": "101", "endLines": "9", "endColumns": "12", "endOffsets": "399"}, "to": {"startLines": "28", "startColumns": "4", "startOffsets": "1525", "endLines": "33", "endColumns": "12", "endOffsets": "1823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c0e0dd431addcd28d83cc92e48e3c0db\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "34,35,36,37,38,39,40,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1828,1898,1982,2066,2162,2264,2366,6070", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1893,1977,2061,2157,2259,2361,2455,6154"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2,8", "startColumns": "4,4", "startOffsets": "100,365", "endLines": "5,21", "endColumns": "12,12", "endOffsets": "311,1124"}, "to": {"startLines": "24,67", "startColumns": "4,4", "startOffsets": "1413,5348", "endLines": "27,80", "endColumns": "12,12", "endOffsets": "1520,6065"}}, {"source": "E:\\XM\\rsbjk-a\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "54,8,10,30,32,28,14,56,16,12,42,40,22,36,48,46,38,20,52,4,6,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2891,492,599,1649,1758,1546,901,3034,1053,750,2292,2173,1320,1925,2595,2478,2048,1232,2780,203,348,1413", "endColumns": "80,61,63,62,54,51,63,87,65,63,57,57,54,58,67,59,54,50,68,55,57,51", "endOffsets": "2967,549,658,1707,1808,1593,960,3117,1114,809,2345,2226,1370,1979,2658,2533,2098,1278,2844,254,401,1460"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,262,325,380,432,496,584,650,714,772,830,885,944,1012,1072,1127,1178,1247,1303,1361", "endColumns": "80,61,63,62,54,51,63,87,65,63,57,57,54,58,67,59,54,50,68,55,57,51", "endOffsets": "131,193,257,320,375,427,491,579,645,709,767,825,880,939,1007,1067,1122,1173,1242,1298,1356,1408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31bbd90c8901d0911f3ed7bb95959bd7\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2460,2535,2646,2735,2836,2943,3050,3149,3256,3359,3486,3574,3698,3800,3902,4018,4120,4234,4362,4478,4600,4736,4856,4990,5110,5222,6159,6276,6400,6530,6652,6790,6924,7040", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2530,2641,2730,2831,2938,3045,3144,3251,3354,3481,3569,3693,3795,3897,4013,4115,4229,4357,4473,4595,4731,4851,4985,5105,5217,5343,6271,6395,6525,6647,6785,6919,7035,7155"}}]}]}