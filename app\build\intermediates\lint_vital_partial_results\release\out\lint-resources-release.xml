http://schemas.android.com/apk/res-auto;;${\:app*release*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*sourceProvider*0*resDir*0}/values-night/colors.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/button_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/play_pause_button_state.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/recording_status_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/settings_icon.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/button_outline.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/recording_status_icon.xml,${\:app*release*sourceProvider*0*resDir*0}/drawable/text_stroke_background.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/activity_camera_preview.xml,${\:app*release*sourceProvider*0*resDir*0}/layout/item_video_record.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml,${\:app*release*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*sourceProvider*0*resDir*0}/values/picker_styles.xml,${\:app*release*sourceProvider*0*resDir*0}/values-night/picker_styles.xml,${\:app*release*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*release*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:colorPrimary,0,V"#3F51B5";colorPrimaryDark,0,V"#303F9F";dialog_label_text_color,0,V"#9C9C9C";dialog_label_text_color,1,V"#D8D8D8";background_light,0,V"#DEE2E5";background_light,1,V"#323232";background_primary_adaptive,0,V"#DEE2E5";background_primary_adaptive,1,V"#202020";button_text_unselected_adaptive,0,V"#808080";button_text_unselected_adaptive,1,V"#808080";text_secondary_adaptive,0,V"#666666";text_secondary_adaptive,1,V"#CCCCCC";button_text_unselected,0,V"@color/button_text_unselected_adaptive";button_text_unselected,1,V"@color/button_text_unselected_adaptive";colorAccent,0,V"#FF4081";background_secondary_adaptive,0,V"#DEE2E5";background_secondary_adaptive,1,V"#202020";white,0,V"#FFFFFF";button_background_selected,0,V"#44676767";button_background_selected,1,V"#44676767";container_background_adaptive,0,V"#F5F5F5";container_background_adaptive,1,V"#1A1A1A";number_picker_text_color,0,V"#000000";number_picker_text_color,1,V"#000000";text_adaptive,0,V"@color/text_primary_adaptive";text_adaptive,1,V"@color/text_primary_adaptive";separator_line_color,0,V"#9C9C9C";separator_line_color,1,V"#D8D8D8";text_primary_adaptive,0,V"#000000";text_primary_adaptive,1,V"#FFFFFF";player_control_text_color,0,V"#FFFFFF";player_control_text_color,1,V"#FFFFFF";button_outline_color,0,V"@color/colorPrimary";button_outline_color,1,V"#FFFFFF";status_bar_color,0,V"#DEE2E5";status_bar_color,1,V"#202020";button_text_selected_adaptive,0,V"#000000";button_text_selected_adaptive,1,V"#FFFFFF";dialog_background_color,0,V"#CED0D1";dialog_background_color,1,V"#858585";+drawable:button_background,2,F;play_pause_button_state,3,F;recording_status_background,4,F;settings_icon,5,F;ic_launcher_foreground,6,F;ic_launcher_background,7,F;button_outline,8,F;recording_status_icon,9,F;text_stroke_background,10,F;+id:video_playback_page,11,F;left_camera_container,11,F;camera_surface_view,12,F;tv_datetime_display,11,F;back_camera_view,11,F;tv_recording_duration,11,F;recording_status_container,11,F;content_container,11,F;btn_sentinel_monitor,11,F;front_camera_container,11,F;btn_filter_left,11,F;tv_current_time,11,F;btn_filter_front,11,F;btn_filter_right,11,F;video_player_page,11,F;btn_time_filter_all,11,F;btnStartStop,12,F;bottom_control_bar,11,F;sentinel_monitor_page,11,F;right_preview_area,11,F;page_selector_container,11,F;btn_month_picker,11,F;left_control_panel,11,F;switch_sentry_auto,11,F;btn_settings,11,F;right_camera_view,11,F;video_player_container,11,F;live_monitor_page,11,F;back_camera_container,11,F;left_camera_view,11,F;btn_playback_speed,11,F;btn_filter_back,11,F;tv_camera_direction,13,F;video_progress_bar,11,F;video_player_card,11,F;recycler_video_list,11,F;main_video_player,11,F;right_camera_container,11,F;btn_day_picker,11,F;iv_recording_status_icon,11,F;btn_video_playback,11,F;player_controls_overlay,11,F;right_content_container,11,F;tv_record_time,13,F;btn_all_cameras,11,F;btn_hour_picker,11,F;btn_filter_all,11,F;front_camera_view,11,F;tv_recording_status,11,F;btn_play_pause,11,F;+layout:activity_main,11,F;activity_camera_preview,12,F;item_video_record,13,F;+mipmap:ic_launcher_round,14,F;ic_launcher_round,15,F;ic_launcher_round,16,F;ic_launcher_round,17,F;ic_launcher_round,18,F;ic_launcher_round,19,F;ic_launcher,20,F;ic_launcher,21,F;ic_launcher,22,F;ic_launcher,23,F;ic_launcher,24,F;ic_launcher,25,F;+string:app_name,26,V"哨兵监控";+style:NumberPickerStyle,27,VNandroid\:textSize:18sp,android\:textColor:@color/number_picker_text_color,android\:background:@android\:color/transparent,android\:gravity:center,;NumberPickerStyle,28,VNandroid\:textSize:18sp,android\:textColor:@color/number_picker_text_color,android\:background:@android\:color/transparent,android\:gravity:center,;CustomButton,29,VDWidget.AppCompat.Button.Borderless,android\:textSize:14sp,android\:padding:0dp,android\:textColor:@color/colorPrimary,android\:drawableTint:@color/white,android\:minWidth:0dp,android\:minHeight:0dp,android\:includeFontPadding:false,;Theme.Sbjk,29,VDTheme.AppCompat.DayNight.NoActionBar,colorPrimary:@color/colorPrimary,colorPrimaryDark:@color/colorPrimaryDark,colorAccent:@color/colorAccent,android\:windowFullscreen:false,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:navigationBarColor:@color/colorPrimaryDark,android\:statusBarColor:@color/background_light,android\:windowLightStatusBar:true,android\:windowBackground:@color/background_light,;Theme.Sbjk,30,VDTheme.AppCompat.DayNight.NoActionBar,colorPrimary:@color/colorPrimary,colorPrimaryDark:@color/colorPrimaryDark,colorAccent:@color/colorAccent,android\:windowFullscreen:false,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:navigationBarColor:@color/background_light,android\:statusBarColor:@color/background_light,android\:windowBackground:@color/background_light,;+xml:data_extraction_rules,31,F;backup_rules,32,F;