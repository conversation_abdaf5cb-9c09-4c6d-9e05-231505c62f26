package com.autolink.sbjk;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.SurfaceTexture;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.TextureView;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.VideoView;

import android.widget.SeekBar;
import android.net.Uri;
import android.os.Handler;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.di.DIContainer;
import com.autolink.sbjk.model.entity.CameraInfo;
import com.autolink.sbjk.model.state.CameraState;
import com.autolink.sbjk.service.CameraService;
import com.autolink.sbjk.viewmodel.MainViewModel;
import com.autolink.sbjk.viewmodel.MainViewModelFactory;
import com.autolink.sbjk.vehicle.VehicleRecordingController;

// 回放功能相关导入
import com.autolink.sbjk.viewmodel.PlaybackViewModel;
import com.autolink.sbjk.adapter.VideoListAdapter;
import com.autolink.sbjk.ui.FilterButtonManager;
import com.autolink.sbjk.ui.TimePickerManager;
import com.autolink.sbjk.ui.PlaybackSpeedManager;
import com.autolink.sbjk.model.VideoRecordInfo;
import com.autolink.sbjk.lifecycle.PlaybackLifecycleManager;
import com.autolink.sbjk.common.theme.ThemeManager;
import com.autolink.sbjk.common.theme.ThemeApplier;

/**
 * 主界面Activity - MVVM架构中的View层
 * 职责：UI展示、用户交互、状态观察
 *
 * 主题管理重构说明：
 * - 实现ThemeManager.ThemeChangeListener接口，统一处理主题变化
 * - 移除原有分散的主题检测逻辑，使用ThemeManager集中管理
 * - 替换硬编码颜色值为ThemeManager.ThemeColors引用
 * - 使用ThemeApplier统一应用主题到UI组件
 */
public class MainActivity extends AppCompatActivity implements ThemeManager.ThemeChangeListener {
    private static final String TAG = "MainActivity";
    private static final int REQUEST_PERMISSIONS = 1;

    // 状态保存键值
    private static final String KEY_SENTRY_MODE = "sentry_mode_enabled";
    private static final String KEY_CURRENT_PAGE = "current_page_sentinel";

    // ViewModel
    private MainViewModel mainViewModel;

    // UI控件
    private SurfaceView frontCameraView;
    private SurfaceView backCameraView;
    private SurfaceView leftCameraView;
    private SurfaceView rightCameraView;
    private Button btnAllCameras;
    private Switch switchSentryAuto;

    // 录像状态显示控件
    private android.widget.ImageView ivRecordingStatusIcon;
    private TextView tvRecordingStatus;
    private TextView tvRecordingDuration;

    // 页面切换相关控件
    private TextView btnSentinelMonitor;
    private TextView btnVideoPlayback;
    private LinearLayout sentinelMonitorPage;
    private LinearLayout videoPlaybackPage;

    // 右侧页面切换相关控件
    private androidx.constraintlayout.widget.ConstraintLayout liveMonitorPage;
    private LinearLayout videoPlayerPage;

    // 回放功能生命周期管理器
    private PlaybackLifecycleManager playbackLifecycleManager;

    // 回放功能UI控件（不受生命周期影响的UI组件）
    private RecyclerView recyclerVideoList;
    private com.autolink.sbjk.ui.widget.FullScreenVideoView mainVideoPlayer;
    private SeekBar videoProgressBar;
    private TextView tvCurrentTime;
    private Handler progressUpdateHandler = new Handler(Looper.getMainLooper());
    private Runnable progressUpdateRunnable;

    // 时间显示相关
    private TextView tvDatetimeDisplay;
    private Handler timeUpdateHandler = new Handler(Looper.getMainLooper());
    private Runnable timeUpdateRunnable;

    // 长按处理
    private Handler longPressHandler = new Handler(Looper.getMainLooper());
    private Runnable stopRunnable;
    private Toast longPressToast;

    // 服务连接
    private CameraService cameraService;
    private boolean bound = false;

    // 哨兵模式状态
    private boolean sentryModeEnabled = false;

    // 主题管理相关
    private ThemeManager themeManager;
    private ThemeManager.ThemeColors currentColors;
    private ServiceConnection connection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            CameraService.LocalBinder binder = (CameraService.LocalBinder) service;
            cameraService = binder.getService();
            bound = true;
            LogUtil.d(TAG, "Camera service connected");
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            bound = false;
            LogUtil.d(TAG, "Camera service disconnected");
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化主题管理器 - 修复监听器注册时机
        themeManager = ThemeManager.getInstance();
        themeManager.addThemeChangeListener(this);  // 先注册监听器，确保能接收初始化通知
        themeManager.initialize(this);              // 再初始化，触发主题状态检测和通知
        LogUtil.d(TAG, "ThemeManager initialized with listener pre-registered");

        // 初始化回放生命周期管理器
        playbackLifecycleManager = PlaybackLifecycleManager.getInstance();
        playbackLifecycleManager.registerAppLifecycle(getApplication());

        // 设置生命周期结束监听器
        playbackLifecycleManager.setOnLifecycleEndListener(reason -> {
            LogUtil.d(TAG, "收到生命周期结束通知，原因: " + reason);
            // 在主线程中切换到哨兵监控页面
            runOnUiThread(() -> {
                switchToPage(true); // 强制切换到哨兵监控页面
            });
        });

        // 使用ViewModelFactory确保配置更改时状态保持
        MainViewModelFactory factory = new MainViewModelFactory(this);
        mainViewModel = new ViewModelProvider(this, factory).get(MainViewModel.class);
        LogUtil.d(TAG, "MainViewModel created with ViewModelProvider and custom factory");

        // 设置状态栏主题
        setupStatusBar();

        setContentView(R.layout.activity_main);

        // 初始化UI和观察者
        initViews();
        setupObservers();

        // 恢复保存的状态
        restoreInstanceState(savedInstanceState);

        // 启动服务
        startCameraService();

        // 检查权限
        checkPermissions();

        LogUtil.d(TAG, "MainActivity created with ViewModelFactory");

        // 兜底机制：确保主题在UI完全初始化后被应用
        new Handler(Looper.getMainLooper()).post(this::ensureThemeApplied);
    }

    // ===== 主题管理接口实现 =====

    /**
     * 主题变化回调 - ThemeManager.ThemeChangeListener接口实现
     *
     * 替换原有的分散主题更新逻辑，统一在此处理所有UI组件的主题变化
     *
     * @param isDarkMode 是否为深色模式
     * @param colors 当前主题颜色配置
     */
    @Override
    public void onThemeChanged(boolean isDarkMode, ThemeManager.ThemeColors colors) {
        currentColors = colors;
        LogUtil.d(TAG, "Theme changed to: " + (isDarkMode ? "Dark" : "Light") + " mode");

        // 确保在主线程中更新UI
        runOnUiThread(this::applyCurrentTheme);
    }

    /**
     * 确保主题被正确应用的兜底机制
     *
     * 解决首次启动时监听器注册时机问题导致的主题未应用情况
     */
    private void ensureThemeApplied() {
        if (currentColors == null) {
            currentColors = themeManager.getCurrentColors();
            LogUtil.w(TAG, "CurrentColors was null, retrieved from ThemeManager");
        }

        if (currentColors != null) {
            applyCurrentTheme();
            LogUtil.d(TAG, "Ensured theme application completed");
        } else {
            LogUtil.e(TAG, "Failed to ensure theme application - colors still null");
        }
    }

    /**
     * 应用当前主题到所有UI组件
     *
     * 替换原有的updateThemeBackground()方法，统一处理所有主题相关的UI更新
     */
    private void applyCurrentTheme() {
        if (currentColors == null) {
            currentColors = themeManager.getCurrentColors();
            if (currentColors == null) {
                LogUtil.w(TAG, "Current colors is null, skipping theme application");
                return;
            }
        }

        try {
            // 设置状态栏主题
            setupStatusBar();

            // 应用主题到各个组件
            applyThemeToMainComponents();
            applyThemeToPageButtons();
            applyThemeToTextViews();
            applyThemeToCameraContainers();

            // 如果录像回放页面激活，应用主题
            if (playbackLifecycleManager != null && playbackLifecycleManager.isPlaybackActive()) {
                applyThemeToPlaybackComponents();
            }

            LogUtil.d(TAG, "Theme applied to all components successfully");
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying current theme", e);
        }
    }

    /**
     * 初始化UI控件
     */
    private void initViews() {
        // 初始化相机预览控件
        frontCameraView = findViewById(R.id.front_camera_view);
        backCameraView = findViewById(R.id.back_camera_view);
        leftCameraView = findViewById(R.id.left_camera_view);
        rightCameraView = findViewById(R.id.right_camera_view);

        // 初始化控制按钮
        btnAllCameras = findViewById(R.id.btn_all_cameras);
        switchSentryAuto = findViewById(R.id.switch_sentry_auto);

        // 初始化录像状态显示控件
        ivRecordingStatusIcon = findViewById(R.id.iv_recording_status_icon);
        tvRecordingStatus = findViewById(R.id.tv_recording_status);
        tvRecordingDuration = findViewById(R.id.tv_recording_duration);

        // 初始化页面切换控件
        btnSentinelMonitor = findViewById(R.id.btn_sentinel_monitor);
        btnVideoPlayback = findViewById(R.id.btn_video_playback);
        sentinelMonitorPage = findViewById(R.id.sentinel_monitor_page);
        videoPlaybackPage = findViewById(R.id.video_playback_page);

        // 初始化右侧页面切换控件
        liveMonitorPage = findViewById(R.id.live_monitor_page);
        videoPlayerPage = findViewById(R.id.video_player_page);

        // 初始化时间显示控件
        tvDatetimeDisplay = findViewById(R.id.tv_datetime_display);

        // 设置按钮事件
        setupButtonListeners();

        // 设置页面切换事件
        setupPageSwitchListeners();

        // 设置相机预览监听器
        setupCameraPreviewListeners();

        // 页面状态将在restoreInstanceState中初始化

        // 启动时间更新
        startTimeUpdate();
    }

    /**
     * 设置按钮监听器
     */
    private void setupButtonListeners() {
        // 创建长按停止的Runnable - 使用车辆录制控制
        stopRunnable = () -> {
            stopRecordingManual();
            cancelLongPressToast();
        };

        // 点击事件 - 使用车辆条件检查
        btnAllCameras.setOnClickListener(v -> {
            Boolean isRecording = mainViewModel.isAllCamerasRecording.getValue();
            if (isRecording == null || !isRecording) {
                startRecordingWithVehicleCheck();
            } else {
                showLongPressHint();
            }
        });

        // 长按事件
        btnAllCameras.setOnLongClickListener(v -> {
            Boolean isRecording = mainViewModel.isAllCamerasRecording.getValue();
            if (isRecording != null && isRecording) {
                showStoppingHint();
                longPressHandler.postDelayed(stopRunnable, 3000);
                return true;
            }
            return false;
        });

        // 触摸事件（取消长按）
        btnAllCameras.setOnTouchListener((v, event) -> {
            if (event.getAction() == android.view.MotionEvent.ACTION_UP ||
                event.getAction() == android.view.MotionEvent.ACTION_CANCEL) {
                longPressHandler.removeCallbacks(stopRunnable);
                cancelLongPressToast();
            }
            return false;
        });

        // 哨兵自动模式开关监听器
        switchSentryAuto.setOnCheckedChangeListener((buttonView, isChecked) -> {
            setSentryAutoMode(isChecked);
        });

        // 初始化时设置正确的按钮状态
        updateRecordingButtonVisibility(sentryModeEnabled);
    }

    /**
     * 设置ViewModel观察者
     */
    private void setupObservers() {
        // 观察录制状态
        mainViewModel.isAllCamerasRecording.observe(this, this::updateRecordingState);

        // 观察录制时长
        mainViewModel.recordingDuration.observe(this, this::updateRecordingDuration);

        // 观察加载状态
        mainViewModel.isLoading.observe(this, isLoading ->
            btnAllCameras.setEnabled(isLoading == null || !isLoading));

        // 观察错误和成功消息
        mainViewModel.errorMessage.observe(this, this::showErrorMessage);
        mainViewModel.successMessage.observe(this, this::showSuccessMessage);

        // 观察相机状态
        mainViewModel.aggregatedCameraState.observe(this, this::updateCameraState);
    }
    
    /**
     * 设置相机预览监听器
     */
    private void setupCameraPreviewListeners() {
        setupSurfaceListener(CameraConstants.CAMERA_FRONT, frontCameraView);
        setupSurfaceListener(CameraConstants.CAMERA_BACK, backCameraView);
        setupSurfaceListener(CameraConstants.CAMERA_LEFT, leftCameraView);
        setupSurfaceListener(CameraConstants.CAMERA_RIGHT, rightCameraView);
    }

    /**
     * 设置页面切换监听器
     */
    private void setupPageSwitchListeners() {
        if (btnSentinelMonitor != null) {
            btnSentinelMonitor.setOnClickListener(v -> switchToPage(true));
        }
        if (btnVideoPlayback != null) {
            btnVideoPlayback.setOnClickListener(v -> switchToPage(false));
        }
    }

    /**
     * 初始化页面状态
     */
    private void initPageState() {
        // 默认显示哨兵监控页面
        switchToPage(true);
    }

    /**
     * 切换页面
     * @param showSentinelMonitor true显示哨兵监控页面，false显示录像回放页面
     */
    private void switchToPage(boolean showSentinelMonitor) {
        // 验证左侧页面控件是否已初始化
        if (sentinelMonitorPage == null || videoPlaybackPage == null) {
            LogUtil.e(TAG, "Left page containers not initialized");
            return;
        }

        // 验证右侧页面控件是否已初始化
        if (liveMonitorPage == null || videoPlayerPage == null) {
            LogUtil.e(TAG, "Right page containers not initialized");
            return;
        }

        if (showSentinelMonitor) {
            // 检查是否已经在哨兵监控页面
            if (sentinelMonitorPage.getVisibility() == View.VISIBLE) {
                LogUtil.d(TAG, "已经在哨兵监控页面，跳过切换");
                return;
            }

            // 结束录像回放生命周期（如果正在运行）
            endPlaybackLifecycle("切换到哨兵监控");

            // 显示哨兵监控页面（左侧）
            sentinelMonitorPage.setVisibility(View.VISIBLE);
            videoPlaybackPage.setVisibility(View.GONE);

            // 显示实时监控页面（右侧）
            liveMonitorPage.setVisibility(View.VISIBLE);
            videoPlayerPage.setVisibility(View.GONE);

            // 更新按钮状态
            if (btnSentinelMonitor != null && btnVideoPlayback != null) {
                updatePageButtonState(btnSentinelMonitor, btnVideoPlayback);
            }

            // 【增强】确保哨兵监控页面也应用当前主题（提高健壮性）
            if (currentColors != null) {
                applyThemeToMainComponents();
                LogUtil.d(TAG, "Applied current theme to main components after switching to sentinel page");
            }

            LogUtil.d(TAG, "Switched to Sentinel Monitor page");
        } else {
            // 检查是否已经在录像回放页面
            if (videoPlaybackPage.getVisibility() == View.VISIBLE) {
                LogUtil.d(TAG, "已经在录像回放页面，跳过切换");
                return;
            }

            // 显示录像回放页面（左侧）
            sentinelMonitorPage.setVisibility(View.GONE);
            videoPlaybackPage.setVisibility(View.VISIBLE);

            // 显示视频播放器页面（右侧）
            liveMonitorPage.setVisibility(View.GONE);
            videoPlayerPage.setVisibility(View.VISIBLE);

            // 更新按钮状态
            if (btnVideoPlayback != null && btnSentinelMonitor != null) {
                updatePageButtonState(btnVideoPlayback, btnSentinelMonitor);
            }

            // 启动录像回放生命周期
            startPlaybackLifecycle();

            // 【修复】确保录像回放页面应用当前主题
            // 解决在哨兵监控页面切换日夜模式后，打开录像回放页面颜色未更新的问题
            if (currentColors != null) {
                applyThemeToPlaybackComponents();
                LogUtil.d(TAG, "Applied current theme to playback components after page switch");
            }

            LogUtil.d(TAG, "Switched to Video Playback page");
        }
    }

    /**
     * 更新页面按钮状态
     */
    private void updatePageButtonState(TextView activeButton, TextView inactiveButton) {
        if (activeButton == null || inactiveButton == null) {
            LogUtil.w(TAG, "Cannot update page button state: buttons are null");
            return;
        }

        try {
            // 设置选中状态
            activeButton.setSelected(true);
            inactiveButton.setSelected(false);

            // 根据当前主题设置文字颜色
            boolean isDarkMode = (getResources().getConfiguration().uiMode &
                Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES;

            // 使用ThemeManager的颜色配置替代硬编码
            if (currentColors != null) {
                activeButton.setTextColor(currentColors.buttonTextSelected);
                inactiveButton.setTextColor(currentColors.buttonTextUnselected);
            } else {
                // 降级方案：如果currentColors为null，使用原有逻辑
                if (isDarkMode) {
                    activeButton.setTextColor(Color.WHITE);
                    inactiveButton.setTextColor(Color.parseColor("#CCCCCC"));
                } else {
                    activeButton.setTextColor(Color.BLACK);
                    inactiveButton.setTextColor(Color.parseColor("#808080"));
                }
            }

            LogUtil.d(TAG, "Updated page button states - Active: " + activeButton.getText() +
                          ", Inactive: " + inactiveButton.getText() +
                          " (Theme: " + (isDarkMode ? "Dark" : "Light") + ")");
        } catch (Exception e) {
            LogUtil.e(TAG, "Error updating page button states", e);
        }
    }

    /**
     * 启动时间更新
     */
    private void startTimeUpdate() {
        if (tvDatetimeDisplay == null) {
            LogUtil.w(TAG, "DateTime display TextView not initialized");
            return;
        }

        // 创建时间更新任务
        timeUpdateRunnable = new Runnable() {
            @Override
            public void run() {
                updateDateTime();
                // 每秒更新一次
                timeUpdateHandler.postDelayed(this, 1000);
            }
        };

        // 立即更新一次，然后开始定时更新
        updateDateTime();
        timeUpdateHandler.postDelayed(timeUpdateRunnable, 1000);

        LogUtil.d(TAG, "Time update started");
    }

    /**
     * 停止时间更新
     */
    private void stopTimeUpdate() {
        if (timeUpdateHandler != null && timeUpdateRunnable != null) {
            timeUpdateHandler.removeCallbacks(timeUpdateRunnable);
            LogUtil.d(TAG, "Time update stopped");
        }
    }

    /**
     * 更新日期时间显示
     */
    private void updateDateTime() {
        if (tvDatetimeDisplay == null) {
            return;
        }

        try {
            // 格式化当前时间
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss", Locale.CHINA);
            String currentTime = dateFormat.format(new Date());

            // 更新显示
            tvDatetimeDisplay.setText(currentTime);

        } catch (Exception e) {
            LogUtil.e(TAG, "Error updating date time", e);
        }
    }

    /**
     * 为SurfaceView设置监听器
     */
    private void setupSurfaceListener(String cameraId, SurfaceView surfaceView) {
        surfaceView.getHolder().addCallback(new SurfaceHolder.Callback() {
            @Override
            public void surfaceCreated(@NonNull SurfaceHolder holder) {
                LogUtil.d(TAG, "Surface created for camera " + cameraId);

                // 验证Surface有效性
                Surface surface = holder.getSurface();
                if (surface != null && surface.isValid()) {
                    // Surface创建后通知ViewModel
                    mainViewModel.onPreviewSurfaceAvailable(cameraId, surface);
                } else {
                    LogUtil.w(TAG, "Surface created but invalid for camera " + cameraId);
                }
            }

            @Override
            public void surfaceChanged(@NonNull SurfaceHolder holder, int format, int width, int height) {
                LogUtil.d(TAG, "Surface changed for camera " + cameraId +
                         ", size: " + width + "x" + height + ", format: " + format);

                // 验证Surface有效性
                Surface surface = holder.getSurface();
                if (surface != null && surface.isValid()) {
                    // Surface尺寸变化时通知ViewModel
                    mainViewModel.onPreviewSurfaceChanged(cameraId, surface);
                } else {
                    LogUtil.w(TAG, "Surface changed but invalid for camera " + cameraId);
                }
            }

            @Override
            public void surfaceDestroyed(@NonNull SurfaceHolder holder) {
                LogUtil.d(TAG, "Surface destroyed for camera " + cameraId +
                         ", isChangingConfigurations: " + isChangingConfigurations() +
                         ", isFinishing: " + isFinishing());

                // 只有在非配置变化且非正常结束时才通知销毁，避免中断录制
                if (!isChangingConfigurations() && !isFinishing()) {
                    mainViewModel.onPreviewSurfaceDestroyed(cameraId);
                }
            }
        });
    }
    
    /**
     * 状态更新方法
     */
    private void updateRecordingState(Boolean isRecording) {
        if (isRecording != null && btnAllCameras != null) {
            btnAllCameras.setActivated(isRecording);
            LogUtil.d(TAG, "Recording state: " + isRecording);
        }

        // 更新录像状态显示
        updateRecordingStatusDisplay(isRecording);
    }

    private void updateCameraState(CameraState cameraState) {
        if (cameraState != null) {
            LogUtil.d(TAG, "Camera state updated: " + cameraState.toString());
            // 可以在这里添加更多的UI状态更新逻辑
        }
    }

    private void showErrorMessage(String error) {
        if (error != null && !error.isEmpty()) {
            Toast.makeText(this, error, Toast.LENGTH_LONG).show();
            mainViewModel.clearError();
        }
    }

    private void showSuccessMessage(String success) {
        if (success != null && !success.isEmpty()) {
            Toast.makeText(this, success, Toast.LENGTH_SHORT).show();
            mainViewModel.clearSuccess();
        }
    }

    /**
     * 更新录像状态显示
     */
    private void updateRecordingStatusDisplay(Boolean isRecording) {
        if (ivRecordingStatusIcon != null && tvRecordingStatus != null) {
            if (isRecording != null && isRecording) {
                // 录制中状态
                ivRecordingStatusIcon.setActivated(true);
                tvRecordingStatus.setText("录像状态：录制中");
                tvRecordingStatus.setTextColor(ContextCompat.getColor(this, R.color.text_adaptive));
            } else {
                // 未录制状态
                ivRecordingStatusIcon.setActivated(false);
                tvRecordingStatus.setText("录像状态：未录制");
                tvRecordingStatus.setTextColor(ContextCompat.getColor(this, R.color.text_secondary_adaptive));
            }
        }
    }

    /**
     * 更新录像时长显示
     */
    private void updateRecordingDuration(String duration) {
        if (tvRecordingDuration != null && duration != null) {
            tvRecordingDuration.setText("录制时长：" + duration);
        }
    }

    /**
     * Toast辅助方法
     */
    private void showLongPressHint() {
        cancelLongPressToast();
        longPressToast = Toast.makeText(this, "长按3秒以停止所有摄像头", Toast.LENGTH_SHORT);
        longPressToast.show();
    }

    private void showStoppingHint() {
        cancelLongPressToast();
        longPressToast = Toast.makeText(this, "请继续按住，3秒后停止...", Toast.LENGTH_LONG);
        longPressToast.show();
    }

    private void cancelLongPressToast() {
        if (longPressToast != null) {
            longPressToast.cancel();
            longPressToast = null;
        }
    }

    /**
     * 服务管理
     */
    private void startCameraService() {
        Intent intent = new Intent(this, CameraService.class);
        bindService(intent, connection, Context.BIND_AUTO_CREATE);
        startForegroundService(intent);
    }
    
    /**
     * 获取相机对应的SurfaceView
     */
    private SurfaceView getSurfaceViewForCamera(String cameraId) {
        switch (cameraId) {
            case CameraConstants.CAMERA_FRONT:
                return frontCameraView;
            case CameraConstants.CAMERA_BACK:
                return backCameraView;
            case CameraConstants.CAMERA_LEFT:
                return leftCameraView;
            case CameraConstants.CAMERA_RIGHT:
                return rightCameraView;
            default:
                return null;
        }
    }

    /**
     * 【重构】设置状态栏主题
     *
     * 原setupTheme()方法重构版本，移除重复的主题检测逻辑，
     * 使用ThemeManager.ThemeColors替换硬编码颜色值
     */
    private void setupStatusBar() {
        try {
            Window window = getWindow();
            if (window != null && currentColors != null) {
                // 使用ThemeApplier统一应用状态栏主题
                ThemeApplier.applyToStatusBar(window, currentColors, themeManager.isDarkMode());
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to setup status bar theme", e);
        }
    }


    /**
     * 权限管理
     */
    private void checkPermissions() {
        if (!hasPermissions()) {
            ActivityCompat.requestPermissions(this, CameraConstants.REQUIRED_PERMISSIONS, REQUEST_PERMISSIONS);
        }
    }

    private boolean hasPermissions() {
        for (String permission : CameraConstants.REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSIONS) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            if (!allGranted) {
                Toast.makeText(this, "需要相机和存储权限才能使用此功能", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 生命周期管理
     */
    @Override
    protected void onResume() {
        super.onResume();

        // 【重构】移除setupTheme()调用
        // ThemeManager在onCreate()中已初始化，会自动处理主题状态
        // 如果需要确保主题状态最新，可以调用themeManager.updateThemeState(this)
        themeManager.updateThemeState(this);

        if (!bound) {
            startCameraService();
        }
        // 恢复时间更新
        startTimeUpdate();

        // 恢复Surface预览 - 延迟执行确保Surface已准备好
        if (mainViewModel != null) {
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                restoreSurfacePreviews();
            }, 500);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        cancelLongPressCallbacks();
        // 暂停时间更新以节省资源
        stopTimeUpdate();

        // 暂停Surface预览但保持录制
        if (mainViewModel != null) {
            pauseSurfacePreviews();
        }
    }

    @Override
    protected void onDestroy() {
        // 移除主题管理器监听器，防止内存泄漏
        if (themeManager != null) {
            themeManager.removeThemeChangeListener(this);
            LogUtil.d(TAG, "ThemeManager listener removed");
        }

        // 结束录像回放生命周期
        endPlaybackLifecycle("Activity销毁");

        // 注销应用生命周期监听
        if (playbackLifecycleManager != null) {
            playbackLifecycleManager.unregisterAppLifecycle();
        }

        cancelLongPressCallbacks();
        stopTimeUpdate();
        stopProgressUpdate(); // 停止播放进度更新
        if (bound) {
            unbindService(connection);
            bound = false;
        }
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        moveTaskToBack(true);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // 【重构】使用ThemeManager统一处理配置变化
        // 替换原有的setupTheme()调用，ThemeManager会自动通知所有监听器
        themeManager.updateThemeState(this);

        // 【重构说明】移除手动的UI更新调用
        // 主题变化会通过ThemeManager.ThemeChangeListener.onThemeChanged()自动处理
        // 不再需要手动调用applyCurrentThemeToPlaybackUI()

        LogUtil.d(TAG, "Configuration changed, theme update delegated to ThemeManager");
    }

    /**
     * Surface预览管理方法
     */
    private void pauseSurfacePreviews() {
        LogUtil.d(TAG, "Pausing surface previews for background transition");
        // 通知ViewModel暂停预览但保持录制
        mainViewModel.pauseAllPreviews();
    }

    private void restoreSurfacePreviews() {
        LogUtil.d(TAG, "Restoring surface previews after foreground transition");

        // 检查并恢复每个Surface的预览
        restoreSingleSurfacePreview(CameraConstants.CAMERA_FRONT, frontCameraView);
        restoreSingleSurfacePreview(CameraConstants.CAMERA_BACK, backCameraView);
        restoreSingleSurfacePreview(CameraConstants.CAMERA_LEFT, leftCameraView);
        restoreSingleSurfacePreview(CameraConstants.CAMERA_RIGHT, rightCameraView);
    }

    private void restoreSingleSurfacePreview(String cameraId, SurfaceView surfaceView) {
        if (surfaceView != null && surfaceView.getHolder() != null) {
            Surface surface = surfaceView.getHolder().getSurface();
            if (surface != null && surface.isValid()) {
                LogUtil.d(TAG, "Restoring preview for camera " + cameraId);
                // 重新设置Surface以确保预览恢复
                mainViewModel.onPreviewSurfaceAvailable(cameraId, surface);
            } else {
                LogUtil.w(TAG, "Surface invalid for camera " + cameraId + ", will wait for surfaceCreated callback");
            }
        }
    }

    /**
     * 辅助方法
     */
    private void cancelLongPressCallbacks() {
        if (longPressHandler != null && stopRunnable != null) {
            longPressHandler.removeCallbacks(stopRunnable);
        }
        if (longPressToast != null) {
            longPressToast.cancel();
            longPressToast = null;
        }
    }

    /**
     * 【重构】应用主题到主要组件
     *
     * 替换原updateThemeBackground()方法中的背景设置逻辑
     * 移除重复的主题检测，使用ThemeManager.ThemeColors替换硬编码颜色
     */
    private void applyThemeToMainComponents() {
        if (currentColors == null) {
            LogUtil.w(TAG, "Current colors is null, skipping main components theme application");
            return;
        }

        try {
            // 左侧控制区域背景 - 使用backgroundPrimary颜色（日间#DEE2E5，夜间#000000）
            View leftControlPanel = findViewById(R.id.left_control_panel);
            ThemeApplier.applyToBackground(leftControlPanel, currentColors, true);

            // 右侧预览区域背景 - 使用backgroundPrimary颜色（日间#DEE2E5，夜间#000000）
            View previewArea = findViewById(R.id.right_preview_area);
            ThemeApplier.applyToBackground(previewArea, currentColors, true);

            // 录像回放页面背景 - 使用backgroundPrimary颜色（日间#DEE2E5，夜间#000000）
            LinearLayout videoPlaybackPage = findViewById(R.id.video_playback_page);
            ThemeApplier.applyToBackground(videoPlaybackPage, currentColors, true);

            // 视频播放器页面背景 - 使用backgroundPrimary颜色（日间#DEE2E5，夜间#000000）
            LinearLayout videoPlayerPage = findViewById(R.id.video_player_page);
            ThemeApplier.applyToBackground(videoPlayerPage, currentColors, true);

            LogUtil.d(TAG, "Applied theme to main components");
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to main components", e);
        }
    }



    /**
     * 【重构】应用主题到页面按钮
     *
     * 替换原updatePageTextColors()方法，移除重复的主题检测和硬编码颜色
     * 使用ThemeApplier.applyToPageButtons()统一处理页面按钮主题
     */
    private void applyThemeToPageButtons() {
        if (btnSentinelMonitor == null || btnVideoPlayback == null || currentColors == null) {
            LogUtil.w(TAG, "Page buttons or colors is null, skipping page buttons theme application");
            return;
        }

        try {
            boolean sentinelSelected = btnSentinelMonitor.isSelected();

            // 设置选中状态
            btnSentinelMonitor.setSelected(sentinelSelected);
            btnVideoPlayback.setSelected(!sentinelSelected);

            // 应用主题颜色 - 使用buttonTextSelected（日间#000000，夜间#FFFFFF）和buttonTextUnselected（日间#808080，夜间#808080）
            if (sentinelSelected) {
                // 哨兵监控页面被选中 - btnSentinelMonitor使用buttonTextSelected，btnVideoPlayback使用buttonTextUnselected
                ThemeApplier.applyToPageTextViews(btnSentinelMonitor, btnVideoPlayback, currentColors);
            } else {
                // 录像回放页面被选中 - btnVideoPlayback使用buttonTextSelected，btnSentinelMonitor使用buttonTextUnselected
                ThemeApplier.applyToPageTextViews(btnVideoPlayback, btnSentinelMonitor, currentColors);
            }

            LogUtil.d(TAG, "Applied theme to page buttons - Sentinel selected: " + sentinelSelected);
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to page buttons", e);
        }
    }



    /**
     * 【重构】应用主题到文本视图
     *
     * 替换原updateDateTimeTextColor()和updateSentryFunctionTextColor()方法
     * 统一处理所有文本视图的主题应用
     */
    private void applyThemeToTextViews() {
        if (currentColors == null) {
            LogUtil.w(TAG, "Current colors is null, skipping text views theme application");
            return;
        }

        try {
            // 时间显示 - 使用textPrimary颜色（日间#000000，夜间#FFFFFF）
            ThemeApplier.applyToTextView(tvDatetimeDisplay, currentColors, true);

            // 哨兵功能文字 - 使用textPrimary颜色（日间#000000，夜间#FFFFFF）
            LinearLayout sentinelPage = findViewById(R.id.sentinel_monitor_page);
            if (sentinelPage != null) {
                TextView sentryFunctionText = findTextViewByText(sentinelPage, "自动启动哨兵功能");
                ThemeApplier.applyToTextView(sentryFunctionText, currentColors, true);
            }

            // 播放控制相关文字
            Button btnTimeFilterAll = findViewById(R.id.btn_time_filter_all);
            ThemeApplier.applyToTextView(btnTimeFilterAll, currentColors, true);

            // 播放时间显示 - 使用播放器控制栏专用颜色
            TextView tvCurrentTime = findViewById(R.id.tv_current_time);
            if (tvCurrentTime != null) {
                tvCurrentTime.setTextColor(ContextCompat.getColor(this, R.color.player_control_text_color));
            }

            // 录像状态显示文字 - 根据录制状态使用不同颜色
            if (tvRecordingStatus != null) {
                // 根据当前录制状态设置颜色
                Boolean isRecording = mainViewModel.isAllCamerasRecording.getValue();
                if (isRecording != null && isRecording) {
                    ThemeApplier.applyToTextView(tvRecordingStatus, currentColors, true);
                } else {
                    tvRecordingStatus.setTextColor(ContextCompat.getColor(this, R.color.text_secondary_adaptive));
                }
            }

            // 录像时长显示文字 - 使用次要文本颜色
            if (tvRecordingDuration != null) {
                tvRecordingDuration.setTextColor(ContextCompat.getColor(this, R.color.text_secondary_adaptive));
            }

            LogUtil.d(TAG, "Applied theme to text views");
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to text views", e);
        }
    }





    /**
     * 递归查找包含指定文字的TextView
     */
    private TextView findTextViewByText(View parent, String targetText) {
        if (parent instanceof TextView) {
            TextView textView = (TextView) parent;
            if (targetText.equals(textView.getText().toString())) {
                return textView;
            }
        } else if (parent instanceof ViewGroup) {
            ViewGroup group = (ViewGroup) parent;
            for (int i = 0; i < group.getChildCount(); i++) {
                TextView result = findTextViewByText(group.getChildAt(i), targetText);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 【重构】应用主题到相机容器
     *
     * 替换原updateCameraContainerBackgrounds()方法，移除重复的主题检测和硬编码颜色
     * 使用ThemeApplier.applyToCardViews()批量处理CardView主题
     */
    private void applyThemeToCameraContainers() {
        if (currentColors == null) {
            LogUtil.w(TAG, "Current colors is null, skipping camera containers theme application");
            return;
        }

        try {
            androidx.cardview.widget.CardView[] containers = {
                findViewById(R.id.front_camera_container),
                findViewById(R.id.back_camera_container),
                findViewById(R.id.left_camera_container),
                findViewById(R.id.right_camera_container)
            };

            // 使用containerBackground颜色（日间#F5F5F5，夜间#1A1A1A）应用到相机容器CardView
            ThemeApplier.applyToCardViews(currentColors, containers);

            // 设置圆角半径（保持原有逻辑）
            float cornerRadius = 8 * getResources().getDisplayMetrics().density;
            for (androidx.cardview.widget.CardView container : containers) {
                if (container != null) {
                    container.setRadius(cornerRadius);
                }
            }

            LogUtil.d(TAG, "Applied theme to camera containers");
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to camera containers", e);
        }
    }



    // === 车辆录制控制方法 ===

    /**
     * 手动启动录制（带车辆条件检查）
     */
    private void startRecordingWithVehicleCheck() {
        if (cameraService != null) {
            cameraService.startRecordingWithVehicleCheck(new VehicleRecordingController.ConditionCallback() {
                @Override
                public void onConditionMet() {
                    // 条件满足，录制已启动
                    runOnUiThread(() -> {
                        showToast("开始录制");
                        LogUtil.i(TAG, "Manual recording started with vehicle condition check");
                    });
                }

                @Override
                public void onConditionNotMet(String reason) {
                    // 条件不满足，显示提示
                    runOnUiThread(() -> {
                        showToast(reason);
                        LogUtil.w(TAG, "Recording start failed: " + reason);
                    });
                }
            });
        } else {
            // 服务未连接，降级到普通录制
            LogUtil.w(TAG, "CameraService not connected, falling back to normal recording");
            mainViewModel.startAllCamerasRecording();
        }
    }

    /**
     * 手动停止录制
     */
    private void stopRecordingManual() {
        if (cameraService != null) {
            cameraService.stopRecordingManual();
            LogUtil.i(TAG, "Manual recording stopped");
        } else {
            // 服务未连接，降级到普通停止
            mainViewModel.stopAllCamerasRecording();
        }
    }

    /**
     * 设置哨兵自动模式
     */
    public void setSentryAutoMode(boolean enabled) {
        sentryModeEnabled = enabled;

        // 控制手动录制按钮的显示/隐藏
        updateRecordingButtonVisibility(enabled);

        if (cameraService != null) {
            cameraService.setSentryAutoMode(enabled);
            LogUtil.i(TAG, "Sentry auto mode: " + enabled);

            String message = enabled ? "哨兵模式已开启，满足条件时自动录制" : "哨兵模式已关闭";
            showToast(message);
        } else {
            LogUtil.w(TAG, "CameraService not connected, sentry mode not available");
            showToast("哨兵模式暂不可用");
        }
    }

    /**
     * 获取哨兵模式状态
     */
    public boolean isSentryModeEnabled() {
        return sentryModeEnabled;
    }

    /**
     * 控制录制按钮的显示/隐藏
     */
    private void updateRecordingButtonVisibility(boolean sentryAutoEnabled) {
        if (btnAllCameras != null) {
            if (sentryAutoEnabled) {
                // 开启哨兵自动模式时，隐藏手动录制按钮
                btnAllCameras.setVisibility(View.GONE);
                LogUtil.i(TAG, "Recording button hidden - sentry auto mode enabled");
            } else {
                // 关闭哨兵自动模式时，显示手动录制按钮
                btnAllCameras.setVisibility(View.VISIBLE);
                LogUtil.i(TAG, "Recording button visible - sentry auto mode disabled");
            }
        }
    }

    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    // === 状态保存和恢复方法 ===

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);

        // 保存哨兵模式状态
        outState.putBoolean(KEY_SENTRY_MODE, sentryModeEnabled);

        // 不保存页面状态，确保重启后始终显示哨兵监控页面
        // 录像回放生命周期不应该被恢复

        LogUtil.d(TAG, "State saved - sentryMode: " + sentryModeEnabled +
                      " (页面状态不保存，重启后默认显示哨兵监控)");
    }

    /**
     * 恢复保存的实例状态
     */
    private void restoreInstanceState(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            // 恢复哨兵模式状态
            sentryModeEnabled = savedInstanceState.getBoolean(KEY_SENTRY_MODE, false);

            // 应用恢复的状态
            if (switchSentryAuto != null) {
                switchSentryAuto.setChecked(sentryModeEnabled);
                updateRecordingButtonVisibility(sentryModeEnabled);
            }

            LogUtil.d(TAG, "State restored - sentryMode: " + sentryModeEnabled);
        } else {
            LogUtil.d(TAG, "No saved state to restore");
        }

        // 始终显示哨兵监控页面（录像回放生命周期不恢复）
        switchToPage(true);

        // 确保按钮状态正确更新（延迟执行，确保UI完全初始化）
        new Handler(Looper.getMainLooper()).post(() -> {
            if (btnSentinelMonitor != null && btnVideoPlayback != null) {
                updatePageButtonState(btnSentinelMonitor, btnVideoPlayback);
                LogUtil.d(TAG, "延迟更新按钮状态完成");
            }
        });

        LogUtil.d(TAG, "应用启动，默认显示哨兵监控页面");
    }

    /**
     * 【重构】应用主题到录像回放组件
     *
     * 替换原updatePlaybackUIColors()方法，移除重复的主题检测和硬编码颜色
     * 统一处理录像回放相关的所有UI组件主题应用
     */
    private void applyThemeToPlaybackComponents() {
        if (currentColors == null) {
            LogUtil.w(TAG, "Current colors is null, skipping playback components theme application");
            return;
        }

        try {
            // 录像回放页面背景已在applyThemeToMainComponents()中处理

            // FilterButtonManager已实现ThemeChangeListener，会自动接收主题变化通知
            // 无需手动调用refreshButtonColors()

            // 更新录像列表颜色
            RecyclerView recyclerView = findViewById(R.id.recycler_video_list);
            if (recyclerView != null) {
                ThemeApplier.applyToBackground(recyclerView, currentColors, true);

                VideoListAdapter adapter = playbackLifecycleManager != null ?
                    playbackLifecycleManager.getVideoListAdapter() : null;
                if (adapter != null) {
                    // 注意：这里需要VideoListAdapter也支持主题应用
                    // 暂时保留原有调用，后续会在VideoListAdapter重构中处理
                    adapter.notifyDataSetChanged();
                }
            }

            // 视频播放器页面背景已在applyThemeToMainComponents()中处理

            // 更新播放控制按钮（播放时间等始终为白色，不跟随主题）
            Button btnPlayPause = findViewById(R.id.btn_play_pause);
            Button btnPlaybackSpeed = findViewById(R.id.btn_playback_speed);
            if (btnPlayPause != null) btnPlayPause.setTextColor(Color.WHITE);
            if (btnPlaybackSpeed != null) btnPlaybackSpeed.setTextColor(Color.WHITE);

            // 时间筛选"全部"按钮
            Button btnTimeFilterAll = findViewById(R.id.btn_time_filter_all);
            ThemeApplier.applyToTextView(btnTimeFilterAll, currentColors, true);

            LogUtil.d(TAG, "Applied theme to playback components");
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to playback components", e);
        }
    }









    /**
     * 更新时间筛选"全部"按钮颜色
     */
    private void updateTimeFilterAllButtonColor(boolean isDarkMode) {
        Button btnTimeFilterAll = findViewById(R.id.btn_time_filter_all);
        if (btnTimeFilterAll != null) {
            if (isDarkMode) {
                btnTimeFilterAll.setTextColor(Color.WHITE); // 夜间模式下文字为白色
            } else {
                btnTimeFilterAll.setTextColor(Color.BLACK); // 浅色模式下文字为黑色
            }
        }
    }

    /**
     * 更新时间选择器颜色
     */
    private void updateTimePickerColors(boolean isDarkMode) {

    }



    /**
     * 更新录像列表颜色
     */
    private void updateVideoListColors(boolean isDarkMode) {
        RecyclerView recyclerView = findViewById(R.id.recycler_video_list);
        if (recyclerView != null) {
            // 设置RecyclerView背景 - 使用ThemeApplier统一管理
            if (currentColors != null) {
                ThemeApplier.applyToBackground(recyclerView, currentColors, true);
            }

            // 通知适配器更新颜色
            if (playbackLifecycleManager != null && playbackLifecycleManager.isPlaybackActive()) {
                VideoListAdapter adapter = playbackLifecycleManager.getVideoListAdapter();
                if (adapter != null) {
                    adapter.notifyDataSetChanged();
                }
            }
        }
    }

    /**
     * 更新播放控制栏颜色
     */
    private void updatePlaybackControlColors(boolean isDarkMode) {
        // 更新播放/暂停按钮颜色
        Button btnPlayPause = findViewById(R.id.btn_play_pause);
        if (btnPlayPause != null) {
            btnPlayPause.setTextColor(Color.WHITE); // 按钮文字始终为白色
        }

        // 更新倍速按钮颜色
        Button btnPlaybackSpeed = findViewById(R.id.btn_playback_speed);
        if (btnPlaybackSpeed != null) {
            btnPlaybackSpeed.setTextColor(Color.WHITE); // 倍速按钮文字始终为白色
        }

        // 更新时间显示颜色
        TextView tvCurrentTime = findViewById(R.id.tv_current_time);
        if (tvCurrentTime != null) {
            tvCurrentTime.setTextColor(Color.WHITE); // 时间文字始终为白色
        }

        // 更新进度条颜色
        SeekBar videoProgressBar = findViewById(R.id.video_progress_bar);
        if (videoProgressBar != null) {
            // 进度条在播放器控制栏中始终使用白色主题
            videoProgressBar.getProgressDrawable().setTint(Color.WHITE);
            videoProgressBar.getThumb().setTint(Color.WHITE);
        }
    }

    /**
     * 启动录像回放生命周期
     */
    private void startPlaybackLifecycle() {
        if (playbackLifecycleManager != null) {
            try {
                playbackLifecycleManager.startPlaybackLifecycle(this);

                // 初始化回放UI组件
                initPlaybackUIComponents();

                // 加载回放数据
                loadPlaybackData();

            } catch (Exception e) {
                LogUtil.e(TAG, "启动录像回放生命周期失败", e);
                Toast.makeText(this, "录像回放功能启动失败", Toast.LENGTH_SHORT).show();

                // 回退到哨兵监控页面
                switchToPage(true);
            }
        }
    }

    /**
     * 结束录像回放生命周期
     */
    private void endPlaybackLifecycle(String reason) {
        if (playbackLifecycleManager != null) {
            playbackLifecycleManager.endPlaybackLifecycle(reason);

            // 清理UI状态
            cleanupPlaybackUIState();
        }
    }

    /**
     * 初始化回放UI组件
     */
    private void initPlaybackUIComponents() {
        try {
            // 初始化RecyclerView和适配器
            recyclerVideoList = findViewById(R.id.recycler_video_list);
            VideoListAdapter adapter = playbackLifecycleManager.getVideoListAdapter();
            if (recyclerVideoList != null && adapter != null) {
                recyclerVideoList.setLayoutManager(new LinearLayoutManager(this));
                recyclerVideoList.setAdapter(adapter);
            }

            // 初始化视频播放器
            mainVideoPlayer = findViewById(R.id.main_video_player);

            // 设置视频拉伸铺满播放窗口
            if (mainVideoPlayer != null) {
                // 确保VideoView填满整个容器
                android.view.ViewGroup.LayoutParams params = mainVideoPlayer.getLayoutParams();
                if (params instanceof android.widget.FrameLayout.LayoutParams) {
                    android.widget.FrameLayout.LayoutParams frameParams = (android.widget.FrameLayout.LayoutParams) params;
                    frameParams.width = android.widget.FrameLayout.LayoutParams.MATCH_PARENT;
                    frameParams.height = android.widget.FrameLayout.LayoutParams.MATCH_PARENT;
                    frameParams.gravity = android.view.Gravity.CENTER;
                    mainVideoPlayer.setLayoutParams(frameParams);
                } else {
                    // 如果不是FrameLayout.LayoutParams，使用通用方法
                    params.width = android.view.ViewGroup.LayoutParams.MATCH_PARENT;
                    params.height = android.view.ViewGroup.LayoutParams.MATCH_PARENT;
                    mainVideoPlayer.setLayoutParams(params);
                }

                LogUtil.d(TAG, "FullScreenVideoView布局参数设置完成");
            }

            // 初始化筛选按钮管理器
            FilterButtonManager filterManager = playbackLifecycleManager.getFilterButtonManager();
            if (filterManager != null) {
                initFilterButtons(filterManager);
            }

            // 初始化时间选择器管理器
            TimePickerManager timeManager = playbackLifecycleManager.getTimePickerManager();
            if (timeManager != null) {
                initTimePickers(timeManager);
            }

            // 初始化倍速管理器
            PlaybackSpeedManager speedManager = playbackLifecycleManager.getPlaybackSpeedManager();
            if (speedManager != null) {
                initPlaybackSpeedManager(speedManager);
            }

            // 设置数据观察者
            setupPlaybackObservers();

            // 设置事件监听器
            setupPlaybackListeners();

            // 主题会通过ThemeManager自动应用，无需手动调用

            LogUtil.d(TAG, "回放UI组件初始化完成");
        } catch (Exception e) {
            LogUtil.e(TAG, "回放UI组件初始化失败", e);
        }
    }

    /**
     * 清理回放UI状态
     */
    private void cleanupPlaybackUIState() {
        try {
            // 停止播放进度更新
            stopProgressUpdate();

            // 清理RecyclerView适配器
            if (recyclerVideoList != null) {
                recyclerVideoList.setAdapter(null);
                recyclerVideoList = null;
            }

            // 停止视频播放并清理监听器
            if (mainVideoPlayer != null) {
                if (mainVideoPlayer.isPlaying()) {
                    mainVideoPlayer.stopPlayback();
                }
                // 清理监听器，防止异步回调
                mainVideoPlayer.setOnPreparedListener(null);
                mainVideoPlayer.setOnCompletionListener(null);
                mainVideoPlayer.setOnErrorListener(null);
                mainVideoPlayer = null;
            }

            // 清理其他UI引用
            videoProgressBar = null;
            tvCurrentTime = null;

            LogUtil.d(TAG, "回放UI状态清理完成");
        } catch (Exception e) {
            LogUtil.e(TAG, "清理回放UI状态时出错", e);
        }
    }



    /**
     * 初始化筛选按钮
     */
    private void initFilterButtons(FilterButtonManager filterManager) {
        // 初始化摄像头筛选按钮
        Button btnFilterAll = findViewById(R.id.btn_filter_all);
        Button btnFilterFront = findViewById(R.id.btn_filter_front);
        Button btnFilterBack = findViewById(R.id.btn_filter_back);
        Button btnFilterLeft = findViewById(R.id.btn_filter_left);
        Button btnFilterRight = findViewById(R.id.btn_filter_right);

        filterManager.initCameraButtons(btnFilterAll, btnFilterFront, btnFilterBack, btnFilterLeft, btnFilterRight);
    }

    /**
     * 初始化时间选择器
     */
    private void initTimePickers(TimePickerManager timeManager) {
        // 初始化时间选择器按钮控件
        Button btnMonthPicker = findViewById(R.id.btn_month_picker);
        Button btnDayPicker = findViewById(R.id.btn_day_picker);
        Button btnHourPicker = findViewById(R.id.btn_hour_picker);

        timeManager.initTimePickers(btnMonthPicker, btnDayPicker, btnHourPicker);
    }

    /**
     * 初始化倍速管理器
     */
    private void initPlaybackSpeedManager(PlaybackSpeedManager speedManager) {
        Button btnPlaybackSpeed = findViewById(R.id.btn_playback_speed);

        if (PlaybackSpeedManager.isSpeedControlSupported()) {
            speedManager.init(btnPlaybackSpeed, mainVideoPlayer);

            // 设置倍速变化监听器
            speedManager.setOnSpeedChangeListener((speed, label) -> {
                LogUtil.d(TAG, "播放速度已更改为: " + label);

                // 显示倍速变化提示
                String message = "播放速度: " + label;
                Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
            });
        } else {
            // 不支持倍速功能时隐藏按钮
            if (btnPlaybackSpeed != null) {
                btnPlaybackSpeed.setVisibility(View.GONE);
            }
            LogUtil.w(TAG, "当前设备不支持播放倍速功能");
        }
    }

    /**
     * 设置回放功能的数据观察者
     */
    private void setupPlaybackObservers() {
        PlaybackViewModel viewModel = playbackLifecycleManager.getPlaybackViewModel();
        VideoListAdapter adapter = playbackLifecycleManager.getVideoListAdapter();

        if (viewModel == null || adapter == null) {
            LogUtil.w(TAG, "回放组件未初始化，跳过观察者设置");
            return;
        }

        // 观察录像列表数据
        viewModel.getVideoList().observe(this, videos -> {
            if (videos != null) {
                adapter.submitList(videos);
                LogUtil.d(TAG, "录像列表更新: " + videos.size() + " 个文件");
            }
        });

        // 观察加载状态
        viewModel.getLoadingState().observe(this, isLoading -> {
            LogUtil.d(TAG, "加载状态: " + (isLoading ? "加载中" : "加载完成"));
        });

        // 观察错误信息
        viewModel.getErrorMessage().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(this, "回放错误: " + error, Toast.LENGTH_SHORT).show();
                viewModel.clearError();
            }
        });

        // 观察选中的录像
        viewModel.getSelectedVideo().observe(this, video -> {
            if (video != null) {
                playSelectedVideo(video);
            }
        });

        // 标记Observer已注册
        playbackLifecycleManager.markObserversRegistered();
    }

    /**
     * 设置回放功能的事件监听器
     */
    private void setupPlaybackListeners() {
        PlaybackViewModel viewModel = playbackLifecycleManager.getPlaybackViewModel();
        VideoListAdapter adapter = playbackLifecycleManager.getVideoListAdapter();
        FilterButtonManager filterManager = playbackLifecycleManager.getFilterButtonManager();
        TimePickerManager timeManager = playbackLifecycleManager.getTimePickerManager();

        if (viewModel == null || adapter == null || filterManager == null || timeManager == null) {
            LogUtil.w(TAG, "回放组件未初始化，跳过监听器设置");
            return;
        }

        // 强制重置筛选器到初始状态，确保每次生命周期开始都是全新状态
        filterManager.forceResetToInitialState();

        // 设置录像列表点击事件
        adapter.setOnVideoClickListener(video -> {
            viewModel.selectVideo(video);
        });

        // 设置摄像头筛选按钮事件
        filterManager.setOnFilterChangeListener(new FilterButtonManager.OnFilterChangeListener() {
            @Override
            public void onCameraFilterChanged(String cameraFilter) {
                viewModel.setCameraFilter(cameraFilter);
            }

            @Override
            public void onTimeFilterChanged(String timeFilter) {
                // 不再使用，保留接口兼容性
            }
        });

        // 确保ViewModel也重置到初始状态
        viewModel.setCameraFilter("全部");
        viewModel.resetTimeFilter();

        // 设置时间选择器事件
        timeManager.setOnTimeSelectedListener((month, day, hour) -> {
            viewModel.setTimeFilter(month, day, hour);
        });

        // 设置时间筛选"全部"按钮事件
        Button btnTimeFilterAll = findViewById(R.id.btn_time_filter_all);
        if (btnTimeFilterAll != null) {
            btnTimeFilterAll.setOnClickListener(v -> {
                // 重置时间筛选，显示全部时间的视频
                viewModel.resetTimeFilter();

                // 同时重置TimePickerManager的显示状态
                timeManager.resetTimeFilter();

                LogUtil.d(TAG, "用户点击时间筛选全部按钮");
            });
        }

        // 设置播放控制按钮事件
        setupPlaybackControlButtons();
    }

    /**
     * 设置播放控制按钮事件
     */
    private void setupPlaybackControlButtons() {
        Button btnPlayPause = findViewById(R.id.btn_play_pause);
        videoProgressBar = findViewById(R.id.video_progress_bar);
        tvCurrentTime = findViewById(R.id.tv_current_time);

        if (btnPlayPause != null) {
            btnPlayPause.setOnClickListener(v -> {
                // 检查生命周期状态
                if (!isPlaybackLifecycleActive() || mainVideoPlayer == null) {
                    return;
                }

                if (mainVideoPlayer.isPlaying()) {
                    mainVideoPlayer.pause();
                    btnPlayPause.setText("▶");
                    stopProgressUpdate();

                    // 通知ViewModel暂停状态
                    PlaybackViewModel viewModel = playbackLifecycleManager.getPlaybackViewModel();
                    if (viewModel != null) {
                        viewModel.pausePlayback();
                    }
                } else {
                    mainVideoPlayer.start();
                    btnPlayPause.setText("⏸");
                    startProgressUpdate();

                    // 通知ViewModel播放状态
                    PlaybackViewModel viewModel = playbackLifecycleManager.getPlaybackViewModel();
                    if (viewModel != null) {
                        viewModel.startPlayback();
                    }
                }
            });
        }

        // 设置进度条拖拽事件
        if (videoProgressBar != null) {
            videoProgressBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    if (fromUser && mainVideoPlayer != null) {
                        int duration = mainVideoPlayer.getDuration();
                        int newPosition = (duration * progress) / 100;
                        mainVideoPlayer.seekTo(newPosition);
                    }
                }

                @Override
                public void onStartTrackingTouch(SeekBar seekBar) {
                    stopProgressUpdate();
                }

                @Override
                public void onStopTrackingTouch(SeekBar seekBar) {
                    if (mainVideoPlayer != null && mainVideoPlayer.isPlaying()) {
                        startProgressUpdate();
                    }
                }
            });
        }
    }

    /**
     * 开始进度更新
     */
    private void startProgressUpdate() {
        if (progressUpdateRunnable == null) {
            progressUpdateRunnable = new Runnable() {
                @Override
                public void run() {
                    updateProgress();
                    progressUpdateHandler.postDelayed(this, 1000); // 每秒更新一次
                }
            };
        }
        progressUpdateHandler.post(progressUpdateRunnable);
    }

    /**
     * 停止进度更新
     */
    private void stopProgressUpdate() {
        if (progressUpdateRunnable != null) {
            progressUpdateHandler.removeCallbacks(progressUpdateRunnable);
        }
    }

    /**
     * 更新播放进度
     */
    private void updateProgress() {
        // 检查生命周期状态，防止在生命周期结束后继续更新
        if (!isPlaybackLifecycleActive()) {
            stopProgressUpdate();
            return;
        }

        if (mainVideoPlayer != null && videoProgressBar != null && tvCurrentTime != null) {
            try {
                int currentPosition = mainVideoPlayer.getCurrentPosition();
                int duration = mainVideoPlayer.getDuration();

                if (duration > 0) {
                    int progress = (currentPosition * 100) / duration;
                    videoProgressBar.setProgress(progress);

                    // 更新时间显示
                    String timeText = formatTime(currentPosition);
                    tvCurrentTime.setText(timeText);
                }
            } catch (Exception e) {
                LogUtil.w(TAG, "更新播放进度失败", e);
                stopProgressUpdate(); // 出错时停止更新
            }
        }
    }

    /**
     * 格式化时间显示
     */
    private String formatTime(int milliseconds) {
        int seconds = milliseconds / 1000;
        int minutes = seconds / 60;
        seconds = seconds % 60;
        return String.format("%02d:%02d", minutes, seconds);
    }

    /**
     * 高效的生命周期状态检查（用于异步回调）
     */
    private boolean isPlaybackLifecycleActive() {
        return playbackLifecycleManager != null && playbackLifecycleManager.isPlaybackActive();
    }

    /**
     * 播放选中的录像
     */
    private void playSelectedVideo(VideoRecordInfo video) {
        // 检查生命周期状态
        if (!isPlaybackLifecycleActive()) {
            LogUtil.w(TAG, "录像回放生命周期未激活，跳过播放操作");
            return;
        }

        if (mainVideoPlayer != null && video != null) {
            try {
                // 停止当前播放
                stopProgressUpdate();

                Uri videoUri = Uri.parse(video.getFilePath());
                mainVideoPlayer.setVideoURI(videoUri);

                // 设置播放器准备完成监听
                mainVideoPlayer.setOnPreparedListener(mp -> {
                    // 关键：检查生命周期状态，防止异步回调在生命周期结束后执行
                    if (!isPlaybackLifecycleActive() || mainVideoPlayer == null) {
                        LogUtil.w(TAG, "录像回放生命周期已结束，跳过onPrepared回调");
                        return;
                    }

                    // 设置MediaPlayer缩放模式为拉伸填充
                    try {
                        // 使用 VIDEO_SCALING_MODE_SCALE_TO_FIT 模式，完全填充容器
                        mp.setVideoScalingMode(android.media.MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT);
                        LogUtil.d(TAG, "MediaPlayer缩放模式设置为拉伸填充");
                    } catch (Exception e) {
                        LogUtil.w(TAG, "设置MediaPlayer缩放模式失败", e);
                    }

                    // 重置进度条
                    if (videoProgressBar != null) {
                        videoProgressBar.setProgress(0);
                    }

                    // 更新播放按钮状态
                    Button btnPlayPause = findViewById(R.id.btn_play_pause);
                    if (btnPlayPause != null) {
                        btnPlayPause.setText("⏸");
                    }

                    // 重置倍速到正常速度
                    PlaybackSpeedManager speedManager = playbackLifecycleManager.getPlaybackSpeedManager();
                    if (speedManager != null) {
                        speedManager.resetToNormalSpeed();
                    }

                    // 开始播放和进度更新
                    mainVideoPlayer.start();
                    startProgressUpdate();

                    // 通知ViewModel播放状态
                    PlaybackViewModel viewModel = playbackLifecycleManager.getPlaybackViewModel();
                    if (viewModel != null) {
                        viewModel.startPlayback();
                    }

                    LogUtil.d(TAG, "开始播放录像: " + video.getDisplayTime());
                });

                // 设置播放完成监听
                mainVideoPlayer.setOnCompletionListener(mp -> {
                    // 检查生命周期状态
                    if (!isPlaybackLifecycleActive()) {
                        LogUtil.w(TAG, "录像回放生命周期已结束，跳过onCompletion回调");
                        return;
                    }

                    Button btnPlayPause = findViewById(R.id.btn_play_pause);
                    if (btnPlayPause != null) {
                        btnPlayPause.setText("▶");
                    }

                    // 通知ViewModel暂停状态
                    PlaybackViewModel viewModel = playbackLifecycleManager.getPlaybackViewModel();
                    if (viewModel != null) {
                        viewModel.pausePlayback();
                    }

                    stopProgressUpdate();

                    // 重置进度条到开始位置
                    if (videoProgressBar != null) {
                        videoProgressBar.setProgress(0);
                    }
                    if (tvCurrentTime != null) {
                        tvCurrentTime.setText("00:00");
                    }
                });

            } catch (Exception e) {
                LogUtil.e(TAG, "播放录像失败", e);
                Toast.makeText(this, "播放失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 加载回放数据（在切换到回放页面时调用）
     */
    private void loadPlaybackData() {
        PlaybackViewModel viewModel = playbackLifecycleManager.getPlaybackViewModel();
        if (viewModel != null) {
            viewModel.loadVideos();
            LogUtil.d(TAG, "开始加载回放数据");
        } else {
            LogUtil.w(TAG, "回放ViewModel未初始化，无法加载数据");
        }
    }
}
